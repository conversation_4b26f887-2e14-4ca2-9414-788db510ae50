/**
 * 树形数据管理模块
 * 处理树形结构的数据操作和管理
 */

/**
 * 树形数据管理器类
 */
export class TreeDataManager {
  constructor() {
    this.treeData = []
    this.expandedKeys = []
    this.selectedKeys = []
  }

  /**
   * 初始化树形数据
   */
  initTreeData(mapList) {
    this.treeData = mapList.map(map => ({
      title: map.mapName,
      key: map.mapId,
      isLeaf: false,
      children: []
    }))
    return this.treeData
  }

  /**
   * 加载地图并将位置点添加为子级
   */
  async loadMapWithPositions(mapId, mapList, fetchPositionsByMapId, positionCounters) {
    try {
      // 获取该地图的位置点数据
      const positionData = await fetchPositionsByMapId(mapId)

      // 找到选中的地图信息
      const selectedMap = mapList.find(map => map.mapId === mapId)
      if (!selectedMap) {
        console.error('未找到指定的地图:', mapId)
        return null
      }

      // 重置该地图的位置计数器（基于现有位置数量）
      const existingPositionCount = positionData ? positionData.length : 0
      positionCounters[mapId] = existingPositionCount + 1

      // 只显示选中的地图及其位置点，重新构建树形数据
      const mapNode = {
        title: selectedMap.mapName,
        key: selectedMap.mapId,
        isLeaf: false,
        children: []
      }

      // 将位置点数据添加为地图节点的子级
      if (positionData && positionData.length > 0) {
        mapNode.children = positionData.map(pos => ({
          title: pos.name,
          key: `${mapId}_${pos.uuid}`,
          isLeaf: true,
          mapId: mapId,
          objectId: pos.uuid,
          objectType: 'position',  // 添加objectType属性
          objectData: pos
        }))
      }

      // 只显示当前选中的地图
      this.treeData = [mapNode]

      // 自动展开选中的地图节点
      this.expandedKeys = [mapId]

      console.log(`已加载地图: ${mapId} 及其位置点数据`)
      return { treeData: this.treeData, expandedKeys: this.expandedKeys }
    } catch (error) {
      console.error('加载地图和位置点数据失败:', error)
      throw error
    }
  }

  /**
   * 添加位置点到树形结构
   */
  addPositionToTree(mapId, positionData) {
    // 确保mapId和node.key的类型一致进行比较
    const mapIdStr = mapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === mapId)
    if (mapNode) {
      if (!mapNode.children) {
        mapNode.children = []
      }

      // 使用uuid作为key的一部分
      const keyId = positionData.uuid
      const newNodeKey = `${mapIdStr}_${keyId}`

      // 添加新的位置点节点
      mapNode.children.push({
        title: `${positionData.name}`,
        key: newNodeKey,
        isLeaf: true,
        mapId: mapId,
        objectId: keyId,
        objectType: 'position',
        objectData: positionData
      })

      // 触发重新渲染
      this.treeData = [...this.treeData]

      // 确保地图节点展开
      if (!this.expandedKeys.includes(mapIdStr) && !this.expandedKeys.includes(mapId)) {
        this.expandedKeys.push(mapIdStr)
      }

      return newNodeKey
    }
    return null
  }

  /**
   * 添加路径到树形结构
   */
  addPathToTree(mapId, pathData) {
    // 确保mapId和node.key的类型一致进行比较
    const mapIdStr = mapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === mapId)
    if (mapNode) {
      if (!mapNode.children) {
        mapNode.children = []
      }

      // 使用uuid作为key的一部分
      const keyId = pathData.uuid
      const newNodeKey = `${mapIdStr}_path_${keyId}`

      // 添加新的路径节点
      mapNode.children.push({
        title: `${pathData.name}`,
        key: newNodeKey,
        isLeaf: true,
        mapId: mapId,
        objectId: keyId,
        objectType: 'path',
        objectData: pathData
      })

      // 触发重新渲染
      this.treeData = [...this.treeData]

      // 确保地图节点展开
      if (!this.expandedKeys.includes(mapIdStr) && !this.expandedKeys.includes(mapId)) {
        this.expandedKeys.push(mapIdStr)
      }

      return newNodeKey
    }
    return null
  }

  /**
   * 从树形结构中删除位置点
   */
  removePositionFromTree(mapId, positionUuid) {
    // 确保mapId和node.key的类型一致进行比较
    const mapIdStr = mapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === mapId)

    if (mapNode && mapNode.children) {
      const positionIndex = mapNode.children.findIndex(child =>
        child.objectData && child.objectData.uuid === positionUuid
      )
      if (positionIndex !== -1) {
        const position = mapNode.children[positionIndex]
        console.log(`正在删除位置: ${position.objectData.name} (${positionUuid}) 从地图 ${mapId}`)
        mapNode.children.splice(positionIndex, 1)
        this.treeData = [...this.treeData]
        console.log(`位置删除成功，剩余位置数量: ${mapNode.children.length}`)
        return true
      } else {
        console.warn(`未找到要删除的位置，uuid: ${positionUuid}`)
      }
    } else {
      console.warn(`未找到地图节点: ${mapId}`)
    }
    return false
  }

  /**
   * 根据位置数据精确删除位置点（处理重名情况）
   */
  removePositionByData(mapId, positionData) {
    // 确保mapId和node.key的类型一致进行比较
    const mapIdStr = mapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === mapId)

    if (mapNode && mapNode.children) {
      let positionIndex = -1

      // 优先通过uuid匹配
      if (positionData.uuid) {
        positionIndex = mapNode.children.findIndex(child =>
          child.objectData && child.objectData.uuid === positionData.uuid && child.objectType === 'position'
        )
      }

      if (positionIndex !== -1) {
        const deletedPosition = mapNode.children[positionIndex]
        console.log(`正在删除位置: ${deletedPosition.objectData.name} (uuid: ${deletedPosition.objectData.uuid}) 从地图 ${mapId}`)
        mapNode.children.splice(positionIndex, 1)
        this.treeData = [...this.treeData]
        console.log(`位置删除成功，剩余位置数量: ${mapNode.children.length}`)
        return true
      } else {
        console.warn(`未找到要删除的位置:`, positionData)
      }
    } else {
      console.warn(`未找到地图节点: ${mapId}`)
    }
    return false
  }

  /**
   * 根据路径数据精确删除路径（处理重名情况）
   */
  removePathByData(mapId, pathData) {
    // 确保mapId和node.key的类型一致进行比较
    const mapIdStr = mapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === mapId)

    if (mapNode && mapNode.children) {
      let pathIndex = -1

      // 优先通过uuid匹配
      if (pathData.uuid) {
        pathIndex = mapNode.children.findIndex(child =>
          child.objectData && child.objectData.uuid === pathData.uuid && child.objectType === 'path'
        )
      }

      // 现在所有路径都有uuid，不再需要名称匹配

      if (pathIndex !== -1) {
        const deletedPath = mapNode.children[pathIndex]
        console.log(`正在删除路径: ${deletedPath.objectData.name} (uuid: ${deletedPath.objectData.uuid}) 从地图 ${mapId}`)
        mapNode.children.splice(pathIndex, 1)
        this.treeData = [...this.treeData]
        console.log(`路径删除成功，剩余子节点数量: ${mapNode.children.length}`)
        return true
      } else {
        console.warn(`未找到要删除的路径:`, pathData)
      }
    } else {
      console.warn(`未找到地图节点: ${mapId}`)
    }
    return false
  }

  /**
   * 更新树形数据中的objectData
   */
  updateTreeNodeObjectData(property, value, currentMapId, selectedKeys) {
    if (!currentMapId || !selectedKeys.length) return false

    // 确保类型匹配
    const mapIdStr = currentMapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === currentMapId)
    if (mapNode && mapNode.children) {
      // 通过当前选中的key来查找树节点
      const selectedKey = selectedKeys[0]
      const treeNode = mapNode.children.find(child => child.key === selectedKey)

      if (treeNode && treeNode.objectData) {
        // 更新objectData中的属性
        if (['xcoordinate', 'ycoordinate', 'zcoordinate'].includes(property)) {
          treeNode.objectData[property] = String(value || 0)
        } else {
          treeNode.objectData[property] = value
        }

        // 如果修改的是名称，同时更新树形结构的显示标题
        if (property === 'name') {
          treeNode.title = value
          // 触发树形数据重新渲染
          this.treeData = [...this.treeData]
        }
        return true
      }
    }
    return false
  }

  /**
   * 更新地图名称
   */
  updateMapName(mapId, newName) {
    if (!mapId || !newName) return false

    // 确保类型匹配
    const mapIdStr = mapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === mapId)

    if (mapNode) {
      // 更新地图节点的标题
      mapNode.title = newName

      // 触发树形数据重新渲染
      this.treeData = [...this.treeData]

      return true
    }
    return false
  }

  /**
   * 查找树节点通过uuid或名称
   */
  findTreeNodeByObject(object, currentMapId) {
    if (!object || !currentMapId) return null

    // 确保类型匹配
    const mapIdStr = currentMapId.toString()
    const mapNode = this.treeData.find(node => node.key === mapIdStr || node.key === currentMapId)
    if (mapNode && mapNode.children) {
      // 通过uuid查找对应的树节点
      const treeNode = mapNode.children.find(child => {
        if (!child.objectData) return false

        // 通过uuid匹配
        return object.uuid && child.objectData.uuid === object.uuid
      })

      return treeNode
    }
    return null
  }

  /**
   * 设置选中的节点
   */
  setSelectedKeys(keys) {
    this.selectedKeys = keys
  }

  /**
   * 设置展开的节点
   */
  setExpandedKeys(keys) {
    this.expandedKeys = keys
  }

  /**
   * 设置树形数据
   */
  setTreeData(treeData) {
    this.treeData = treeData
  }

  /**
   * 获取当前树形数据
   */
  getTreeData() {
    return this.treeData
  }

  /**
   * 获取选中的节点
   */
  getSelectedKeys() {
    return this.selectedKeys
  }

  /**
   * 获取展开的节点
   */
  getExpandedKeys() {
    return this.expandedKeys
  }

  /**
   * 清空选择
   */
  clearSelection() {
    this.selectedKeys = []
  }
}
